<script setup lang="ts">
import { useCurrentThreadStore } from "../stores/currentThreadStore";
import { onMounted, onUnmounted, watch } from "vue";
import { useTimer } from "~/hooks/useTimer";
import { formatElapsedTime } from "~/utils/format";
import { SparklesIcon } from "lucide-vue-next";
import ParalleloScan from "@/components/ParalleloScan.vue";
import dayjs from "dayjs";

const currentThreadStore = useCurrentThreadStore();
const { isRunning: isAgentRunning } = storeToRefs(currentThreadStore);

const props = defineProps<{
  reportId?: string | undefined;
  title?: string;
  message?: string;
  status?: string;
}>();

const { startTimer, stopTimer, currentElapsedTime } = useTimer();

const formattedStartTime = computed(() => {
  return dayjs(Date.now()).format("HH:mm");
});

onMounted(() => {
  if (props.reportId) {
    return;
  }

  startTimer();
});

onUnmounted(() => {
  stopTimer();
});

watchEffect(() => {
  if (!isAgentRunning.value) {
    stopTimer();
  }
});

watch(
  () => props.reportId,
  (newVal) => {
    if (newVal) {
      stopTimer();
      currentThreadStore.setArtifactReport(newVal);
    }
  }
);
</script>

<template>
  <div
    class="w-3/5 rounded-2xl border border-[#d9d9d9] bg-[#fafafa] shadow-sm cursor-pointer hover:bg-gray-50 transition-colors overflow-hidden"
    @click="reportId && currentThreadStore.setArtifactReport(reportId)"
  >
    <div v-if="!reportId" class="flex justify-between text-checking-text">
      <!-- 左侧 -->
      <div class="flex-1 p-4 min-w-0">
        <ParalleloScan class="text-sm mb-1">Generating Report</ParalleloScan>
        <div class="text-xs text-default-text mt-2">
          {{ formatElapsedTime(currentElapsedTime) }}
        </div>
      </div>
      <!-- 右侧 -->
      <div class="bg-white pt-3 pl-3 mt-4 shadow-md rounded-tl-lg">
        <ParalleloScan class="flex items-center gap-2 mb-4">
          <SparklesIcon class="h-4 w-4" />
          <span class="text-sm pr-3">AI Report</span>
        </ParalleloScan>
        <div class="space-y-1 w-24 relative -right-3">
          <div class="h-2 bg-title-bg animate-pulse" />
          <div class="h-2 bg-title-bg animate-pulse w-4/5" />
          <div class="h-3 bg-title-bg animate-pulse w-full" />
        </div>
      </div>
    </div>
    <div v-else-if="status !== 'done'" class="p-6">
      <div class="text-checking-text">{{ message }}</div>
    </div>
    <div
      v-else
      class="flex text-checking-text"
      @click="currentThreadStore.setArtifactReport(reportId)"
    >
      <!-- 左侧 -->
      <div class="p-4 flex-1 min-w-0">
        <div class="text-lg font-semibold mb-2 text-black leading-snug">
          <span class="break-all block whitespace-pre-wrap">
            {{ title }}
          </span>
        </div>
        <div class="text-xs text-default-text mt-2">
          Creation time:{{ formattedStartTime }}
        </div>
      </div>
      <!-- 右侧 -->
      <div
        class="bg-white pt-3 pl-3 mt-4 shadow-md rounded-tl-lg text-default-text"
      >
        <div class="flex items-center gap-2 mb-4">
          <SparklesIcon class="h-4 w-4" />
          <span class="text-sm pr-3">AI Report</span>
        </div>
        <div class="space-y-1 w-24 relative -right-3">
          <div class="h-2 bg-title-bg w-full" />
          <div class="h-2 bg-title-bg w-4/5" />
          <div class="h-3 bg-title-bg w-full" />
        </div>
      </div>
    </div>
  </div>
</template>
