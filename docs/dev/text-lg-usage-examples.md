# text-lg 字体层级使用示例

## 概述

本文档展示了在项目中新添加的 `text-lg` (18px) 字体层级的使用场景和最佳实践。

## 已实施的 text-lg 使用场景

### 1. InteractiveToolCall 组件 - 重要问题文字

**位置**: `modules/sentire/components/InteractiveToolCall.vue`

**使用场景**: 交互式工具调用中的问题文字，需要突出显示以引起用户注意

```vue
<p class="text-lg font-medium leading-relaxed text-stone-700">
  {{ props.question }}
</p>
```

**设计理念**: 
- 使用 `text-lg` 提升问题的可读性
- 配合 `font-medium` 增强视觉权重
- `leading-relaxed` 确保良好的行高

### 2. ArtifactReportToolCall 组件 - 报告标题

**位置**: `modules/sentire/components/ArtifactReportToolCall.vue`

**使用场景**: AI 报告的标题显示，作为重要信息的主要标识

```vue
<div class="text-lg font-semibold mb-2 text-black leading-snug">
  <span class="break-all block whitespace-pre-wrap">
    {{ title }}
  </span>
</div>
```

**设计理念**:
- `text-lg` 突出报告标题的重要性
- `font-semibold` 提供适当的字重
- `leading-snug` 保持紧凑的行高

### 3. PlanTasks 组件 - 展开状态标题

**位置**: `modules/sentire/components/PlanTasks.vue`

**使用场景**: 任务列表展开时的标题，提供清晰的模块标识

```vue
<div v-if="isExpanded" class="text-lg font-semibold leading-snug mb-3 text-gray-800">
  Process Tasks
</div>
```

**设计理念**:
- 在展开状态下使用 `text-lg` 强调模块标题
- 与折叠状态形成视觉层次对比

### 4. StandardToolCall 组件 - 工具名称

**位置**: `components/StandardToolCall.vue`

**使用场景**: 工具调用的名称显示，提升工具操作的可识别性

```vue
<div class="flex items-center cursor-pointer gap-x-1 mr-1 text-lg font-medium">
  {{ getToolDisplayName(props.name, props.input) }}
  <!-- ... -->
</div>
```

**设计理念**:
- `text-lg` 使工具名称更加突出
- 配合图标形成完整的视觉识别

### 5. Suggestions 组件 - 建议标题

**位置**: `modules/sentire/components/Suggestions.vue`

**使用场景**: 建议操作区域的标题，为用户提供清晰的功能指引

```vue
<h3 class="text-lg font-semibold leading-snug mb-4 text-gray-800">
  Suggested Actions
</h3>
```

**设计理念**:
- 使用语义化的 `h3` 标签
- `text-lg` 提供适当的视觉层次
- 为建议列表提供清晰的分组标识

### 6. AlertDescription 组件 - 重要提示

**位置**: `components/ui/alert/AlertDescription.vue`

**使用场景**: 重要警告或提示信息，需要用户特别关注

```vue
<!-- 使用 lg 尺寸的警告描述 -->
<AlertDescription size="lg">
  这是一个重要的系统提示信息
</AlertDescription>
```

**设计理念**:
- 提供可选的尺寸变体 (`sm` | `base` | `lg`)
- `lg` 尺寸使用 `text-lg font-medium` 突出重要性

## 使用指南

### 何时使用 text-lg

1. **重要提示文字**: 需要用户特别注意的信息
2. **副标题**: 介于主标题和正文之间的层级
3. **功能模块标题**: 区分不同功能区域的标识
4. **交互元素标签**: 重要的交互操作说明
5. **状态信息**: 需要突出显示的状态或进度信息

### 配合使用的样式

```css
/* 推荐的 text-lg 组合 */

/* 重要提示 */
.text-lg.font-medium.text-primary

/* 副标题 */
.text-lg.font-semibold.leading-snug.mb-3

/* 功能标题 */
.text-lg.font-semibold.leading-snug.mb-4.text-gray-800

/* 交互说明 */
.text-lg.font-medium.leading-relaxed.text-stone-700
```

### 响应式使用

```vue
<!-- 在需要时可以配合响应式设计 -->
<h3 class="text-base md:text-lg lg:text-xl font-semibold">
  响应式副标题
</h3>

<!-- 移动端优先的设计 -->
<p class="text-lg md:text-xl leading-relaxed">
  重要说明文字
</p>
```

## 设计原则

### 1. 层次清晰
- `text-lg` 位于 `text-base` 和 `text-xl` 之间
- 提供渐进的视觉层次
- 避免跳跃式的字体大小变化

### 2. 语义化使用
- 配合适当的 HTML 语义标签
- 考虑内容的重要性和功能
- 保持一致的使用模式

### 3. 可访问性
- 确保足够的对比度
- 配合适当的行高
- 考虑不同设备的显示效果

### 4. 性能考虑
- 使用 Tailwind 标准类
- 避免自定义 CSS
- 保持样式的可维护性

## 迁移建议

### 现有组件升级

如果现有组件中有以下情况，可以考虑升级为 `text-lg`:

```vue
<!-- 升级前 -->
<div class="text-base font-semibold">重要标题</div>

<!-- 升级后 -->
<div class="text-lg font-semibold leading-snug">重要标题</div>
```

```vue
<!-- 升级前 -->
<p class="text-sm font-medium">重要提示</p>

<!-- 升级后 -->
<p class="text-lg font-medium leading-relaxed">重要提示</p>
```

### 注意事项

1. **渐进式升级**: 不要一次性修改所有组件
2. **测试验证**: 确保视觉效果符合预期
3. **保持一致**: 在相似场景中使用相同的样式组合
4. **文档更新**: 及时更新组件文档和使用指南

## 总结

通过在关键组件中添加 `text-lg` 字体层级，我们成功填补了字体层次的缺口，提供了更丰富的视觉表达能力。这些改进遵循了 Context7 和 Tailwind CSS 的最佳实践，确保了设计的一致性和可维护性。
